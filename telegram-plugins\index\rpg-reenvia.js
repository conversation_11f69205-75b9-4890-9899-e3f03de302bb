let handler = async (m, { bot, text, participants, isAdmin, isOwner, usedPrefix, command}) => {
try {
if (!m.quoted) return bot.reply(m.chat, `👀 Responde a un mensaje para poder usar el comando coreectamente.`, m)
await bot.sendMessage(m.chat,  forward: m.quoted.fakeObj )
} catch {
await m.react('✖️')
}}
handler.help = ['reenviar']
handler.tags = ['tools']
handler.command = ['reenviar']

export default handler
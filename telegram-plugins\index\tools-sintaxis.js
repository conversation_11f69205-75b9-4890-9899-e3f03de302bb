const _0x5619d0 = _0x72e1;
(function (_0xd3c81d, _0x392a51) {
    const _0x21088a = _0x72e1, _0x200c43 = _0xd3c81d();
    while (!![]) {
        try {
            const _0x42c080 = -parseInt(_0x21088a(0xdb)) / (-0x37f + 0x8a9 * -0x1 + 0xc29) * (parseInt(_0x21088a(0xd6)) / (0x1ef3 + 0x8 * 0x73 + 0xb83 * -0x3)) + -parseInt(_0x21088a(0xd8)) / (0x5f3 * 0x2 + 0x5f6 + -0x11d9) + parseInt(_0x21088a(0xd2)) / (-0x5f3 + 0x14c4 + -0xecd) + parseInt(_0x21088a(0xce)) / (-0x23c5 + 0x3f * -0x67 + 0x3d23) + parseInt(_0x21088a(0xd1)) / (-0x107 * -0x1 + 0x4ee + -0x5ef) + parseInt(_0x21088a(0xcb)) / (0x5 * -0x469 + 0x1367 * -0x1 + 0x297b) + -parseInt(_0x21088a(0xd4)) / (-0xc15 * 0x1 + 0xe98 + 0x7f * -0x5);
            if (_0x42c080 === _0x392a51)
                break;
            else
                _0x200c43['push'](_0x200c43['shift']());
        } catch (_0x3c20c9) {
            _0x200c43['push'](_0x200c43['shift']());
        }
    }
}(_0x8390, -0x4c5b * -0x8 + 0xed9fa * -0x1 + 0x1a1970));
function _0x72e1(_0x320719, _0x2144ec) {
    const _0x244ae2 = _0x8390();
    return _0x72e1 = function (_0x28f335, _0x4dff79) {
        _0x28f335 = _0x28f335 - (0x25cb + -0xcd3 + 0x97 * -0x29);
        let _0x305458 = _0x244ae2[_0x28f335];
        return _0x305458;
    }, _0x72e1(_0x320719, _0x2144ec);
}
import _0x922da3 from 'axios';
import _0x17c429 from 'form-data';
let handler = async (_0x344882, {bot: _0x23cf63}) => {
    const _0x413566 = _0x72e1, _0x20a53a = {
            'CoXGS': _0x413566(0xd3),
            'etHKE': _0x413566(0xd5)
        };
    try {
        const _0x3e8991 = _0x344882[_0x413566(0xcf)] ? _0x344882[_0x413566(0xcf)] : _0x344882, _0xed8e5 = (_0x3e8991['msg'] || _0x3e8991)[_0x413566(0xe4)] || '';
        if (!_0xed8e5)
            return _0x344882[_0x413566(0xc9)](_0x20a53a[_0x413566(0xcc)]);
        _0x344882[_0x413566(0xc9)](_0x413566(0xca));
        let _0x227117 = await _0x3e8991['download'](), _0x4a5909 = new _0x17c429();
        _0x4a5909['append'](_0x413566(0xdc), _0x227117, 'fgsi_' + Date[_0x413566(0xde)]() + '.' + _0xed8e5[_0x413566(0xe0)]('/')[-0x442 * -0x7 + -0x129 * -0x14 + -0x3501]);
        let {data: _0x3851b3} = await _0x922da3[_0x413566(0xe6)](_0x413566(0xe3), _0x4a5909, {
                'headers': {
                    ..._0x4a5909[_0x413566(0xdf)](),
                    'Content-Type': _0x413566(0xd9),
                    'apikey': _0x20a53a[_0x413566(0xe5)]
                }
            }), _0x13a62b = _0x3851b3[_0x413566(0xdd)];
        _0x344882[_0x413566(0xc9)]('' + _0x13a62b[_0x413566(0xd7)][_0x413566(0xd0)]);
    } catch (_0x1e4786) {
        _0x344882[_0x413566(0xc9)](_0x413566(0xcd) + _0x1e4786['message']);
    }
};
handler['help'] = [_0x5619d0(0xe2)], handler[_0x5619d0(0xe1)] = [_0x5619d0(0xe2)], handler[_0x5619d0(0xda)] = [_0x5619d0(0xe7)];
function _0x8390() {
    const _0x190e18 = [
        'etHKE',
        'post',
        'herramientas',
        'reply',
        '*Subiendo\x20a\x20MediaFire...*',
        '4052622oaFfFQ',
        'CoXGS',
        '⚠️\x20Error:\x20',
        '5695630KaRgmH',
        'quoted',
        'normal_download',
        '6092892eEpdrZ',
        '516880fDYTIP',
        'Responde\x20a\x20un\x20archivo\x20multimedia\x20(imagen/video/audio/documento)',
        '5620000aqRilw',
        'fgsiapi-213d7253-6d',
        '4SsxooT',
        'links',
        '3499140RgZdxK',
        'multipart/form-data',
        'tags',
        '50188GiVQDf',
        'file',
        'data',
        'now',
        'getHeaders',
        'split',
        'command',
        'upmf',
        'https://fgsi.koyeb.app/api/upload/uploadMediaFire',
        'mimetype'
    ];
    _0x8390 = function () {
        return _0x190e18;
    };
    return _0x8390();
}
export default handler;
{"name": "Barboza", "version": "2.2.0", "description": "Super Telegram Bot Multi Device (Converted from WhatsApp)", "main": "index.js", "type": "module", "directories": {"lib": "lib", "src": "storage", "plugins": "plugins"}, "scripts": {"start": "node index.js", "qr": "node index.js qr", "code": "node index.js code", "telegram": "node telegram-index.js", "tg": "node telegram-index.js", "convert-plugins": "node convert-plugins.js all", "setup-telegram": "node setup-telegram.js", "test": "node test.js", "test2": "nodemon index.js", "eslint": "eslint .", "eslintfix": "eslint --fix ."}, "homepage": "https://github.com/meldexzz/bolibotmensual.git", "author": {"name": "meldexzz"}, "repository": {"type": "git", "url": "https://github.com/meldexzz/bolibotmensual.git"}, "bugs": {"url": "https://github.com/meldexzz/bolibotmensual.git"}, "license": "GPL-3.0-or-later", "dependencies": {"@adiwajshing/keyed-db": "^0.2.4", "@bochilteam/scraper": "^4.2.4", "@bochilteam/scraper-sosmed": "^1.0.5", "@ibaraki-douji/pixivts": "^2.4.1", "@octokit/rest": "^21.1.1", "@vitalets/google-translate-api": "^8.0.0", "@whiskeysockets/baileys": "github:EdderBot02/bail", "@xct007/frieren-scraper": "^0.0.2", "acrcloud": "^1.4.0", "api-dylux": "^1.7.2", "aptoide-scraper": "github:DIEGO-OFC/dv-aptoide-scraper", "awesome-phonenumber": "^3.4.0", "axios": "^1.1.2", "boxen": "^7.1.1", "cfonts": "^3.3.0", "chalk": "^5.1.0", "cheerio": "npm:cheerio@1.0.0-rc.12", "didyoumean": "^1.2.2", "express": "^4.18.1", "file-type": "^18.0.0", "fluent-ffmpeg": "^2.1.2", "fluid-spotify.js": "*", "form-data": "^4.0.0", "formdata-node": "^5.0.0", "g-i-s": "^2.1.6", "google-it": "^1.6.4", "google-libphonenumber": "^3.2.38", "gradient-string": "^2.0.2", "hispamemes": "^1.0.7", "human-readable": "^0.2.1", "imagemaker.js": "^2.0.4", "instagram-url-direct": "^1.0.13", "javascript-obfuscator": "^4.1.1", "jsdom": "^20.0.1", "link-preview-js": "^3.0.0", "lodash": "^4.17.21", "lowdb": "^3.0.0", "megajs": "^1.1.3", "mime-types": "^2.1.35", "mongoose": "^6.6.5", "node-cache": "^5.1.2", "node-fetch": "^3.2.10", "node-gtts": "^2.0.2", "node-os-utils": "^1.3.6", "node-telegram-bot-api": "^0.66.0", "node-webpmux": "^3.1.3", "openai": "^3.3.0", "p-limit": "^6.2.0", "pdfkit": "^0.13.0", "performance-now": "^2.1.0", "pino": "^8.6.1", "pino-pretty": "^9.1.1", "qrcode": "^1.5.1", "qrcode-terminal": "^0.12.0", "readline": "^1.3.0", "ruhend-scraper": "^7.0.4", "sanzy-spotifydl": "*", "similarity": "^1.2.1", "socket.io": "^4.5.2", "syntax-error": "^1.4.0", "terminal-image": "^2.0.0", "translate-google-api": "^1.0.4", "url-file-size": "^1.0.5", "url-regex-safe": "^3.0.0", "uuid": "^10.0.0", "yargs": "^17.3.1", "yt-search": "^2.10.3"}, "optionalDependencies": {"moment-timezone": "^0.5.37", "wa-sticker-formatter": "^4.3.2"}}
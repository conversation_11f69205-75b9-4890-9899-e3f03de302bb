
var handler = async (m, { bot, args }) => {
    try {
        let group = m.chat;
        let link = 'https://chat.whatsapp.com/' + await bot.groupInviteCode(group);
        bot.reply(m.chat, '🔗 ' + link, m, { detectLink: true });
    } catch (error) {
        bot.reply(m.chat, 'Error al obtener el enlace del grupo. Asegúrate de que soy administrador y estoy en un grupo.', m);
    }
}

handler.help = ['link'];
handler.tags = ['grupo'];
handler.command = ['link', 'linkgroup'];

handler.group = true;
handler.botAdmin = true;

export default handler;
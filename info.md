# Sasuke-bot-V2 (<PERSON><PERSON>) – Project Documentation

This document explains what Sasuke-bot-V2 is, how to install and run it, how to configure and extend it via plugins, and how to use Docker. It is based on the current codebase.

- Codebase: ESM Node.js WhatsApp bot on Baileys MD
- Primary entry: `index.js` → spawns `main.js`
- Core runtime: `main.js`
- Handler/message router: `handler.js`
- Config: `config.js`
- Plugins: `plugins/`


## 1) Overview & Features

Sasuke-bot-V2 is a modular WhatsApp bot for groups and private chats built on Baileys multi-device. It provides:

- Group utilities and moderation (admin checks, admin-only mode, anti-bot/sub-bot safeguards)
- Media handling (requires ffmpeg/imagick/webp binaries)
- Rich plugin ecosystem (`plugins/` ~400+ commands)
- Multiple authentication methods (QR, 8‑digit pairing, optional mobile mode)
- JSON database persistence via LowDB
- Hot-reload of plugins and the main handler without restarts

Relevant files:
- `main.js` – runtime, auth, plugin loader, hot-reload, cleanup, optional server mode flag
- `handler.js` – command matching, permissions, limits, fail messages, stats
- `config.js` – owners, branding, API endpoints/keys, session paths


## 2) Requirements

- Node.js (LTS recommended)
- System binaries for media features:
  - `ffmpeg`, `ffprobe`, `imagemagick`/`magick`, `gm`, `webp` (tested via `_quickTest()` in `main.js`)

Windows tips: install ffmpeg and ImageMagick, add them to PATH. Linux/macOS: use package managers.


## 3) Installation

```bash
# 1) Install dependencies
npm install

# 2) Optional: install qrcode-terminal (Dockerfile installs it too)
npm install qrcode-terminal
```

Directory notes:
- Sessions: `Sesiones/Principal` (main) and `Sesiones/Subbots`
- Database: `database.json` (can be prefixed at runtime, see §7)
- Temp: `tmp/`
- Additional DB: `db/chatgpt.json`


## 4) Running & Authentication

Scripts from `package.json`:
- `npm start` → `node index.js`
- `npm run qr` → `node index.js qr`
- `npm run code` → `node index.js code`

Runtime flags parsed via `yargs` in `main.js`:
- `qr` – show QR for pairing
- `code` – generate 8‑digit code and/or prompt for phone number
- `mobile` – enable Baileys mobile mode
- `server` – attempts to load `server.js` (see §10 for caveat)
- `prefix` – custom command prefix (overrides default symbol set)
- Misc: `self`, `swonly`, `nyimak`, `restrict`, `autoread`, `queque`, `autocleartmp`

Examples:
```bash
# Pair via QR
npm run qr

# Pair via 8-digit code (will prompt phone)
npm run code

# Custom prefix (Windows)
npm start -- --prefix "!"
```

Authentication details (`main.js`):
- Uses Baileys `useMultiFileAuthState()` with sessions in `global.Sesion` → `Sesiones/Principal`
- First-time run prompts: select QR vs 8‑digit code
- If pairing code flow is chosen, the bot prints a code like `1234-5678` to link the device

Lifecycle behaviors:
- Temp cleanup every ~3 minutes (`tmp/`)
- Automatic soft reset roughly every 45 minutes (IPC `process.send('reset')`)
- Optional server mode via `--server` flag (see §10)


## 5) Configuration (`config.js`)

- __Owners__: `global.owner` – array of owners (numbers/JIDs, display names, boolean flags)
- __Roles__: `global.mods`, `global.prems`
- __Branding__: `global.packname`, `global.author`, `global.stickpack`, `global.stickauth`, `global.wm`, `global.botname`, `global.textbot`, `global.dev`, `global.listo`
- __Links__: `global.group`, `global.canal`, `global.insta`, `global.channel`, `global.namechannel`
- __Assets__: `storage/img/catalogo.png`, `storage/img/miniurl.jpg` read via `fs.readFileSync`
- __Newsletters__: `global.ch` → `{ ch1, ch2 }`
- __Helpers exposed globally__: `cheerio`, `fs`, `fetch`, `axios`
- __Session folders__: `global.jadi = 'Sesiones/Subbots'`, `global.Sesion = 'Sesiones/Principal'`
- __Database filename__: `global.dbname = 'database.json'`
- __Game/economy__: `global.multiplier = 69`, `global.maxwarn = '2'`
- __APIs & keys__: `global.APIs` and `global.APIKeys`

Note: API keys are masked from error outputs in `handler.js`.


## 6) Database & Persistence

LowDB JSON is used in `main.js`:
- Primary DB: `database.json` (default); structure initialized with
  - `users`, `chats`, `stats`, `msgs`, `sticker`, `settings`
- ChatGPT DB: `db/chatgpt.json`

Prefixing DB filename: running with an extra positional argument prefixes the DB file name (see `global.db = new Low(new JSONFile(`${opts._[0] ? opts._[0] + '_' : ''}${global.dbname}`))`). Example:
```bash
node index.js mybot   # uses mybot_database.json
```

Note: A `cloudDBAdapter` is referenced but not imported, so remote DB URLs via `--db` are not supported out-of-the-box.


## 7) Command Prefixes

- Global default: character class of symbols defined in `main.js`
- Customization: `--prefix` CLI flag or set `global.prefijo`
- Per-plugin overrides: `plugin.customPrefix` supports string/regex/array (see §8)


## 8) Plugin System

Loader (in `main.js`):
```js
const pluginFolder = global.__dirname(join(__dirname, './plugins/index'));
// ... reads all .js files in pluginFolder
watch(pluginFolder, global.reload);
```

Important: The current repo places plugins directly under `plugins/` (e.g., `plugins/_Autodetect.js`). There is no `plugins/index/` folder. To load plugins, do one of:
- Create `plugins/index/` and move `.js` plugin files there, or
- Change `pluginFolder` in `main.js` to `./plugins`.

Hot-reload:
- Plugins are syntax-checked (`syntaxerror`) and re-imported on change
- `global.plugins[filename]` is refreshed without a full restart

Plugin anatomy (ESM):
```js
// plugins/echo.js
export default async function (m, {
  conn, args, command, text,
  isROwner, isOwner, isAdmin, isBotAdmin, isPrems
}) {
  await conn.reply(m.chat, text || 'echo', m);
}

// Optional metadata on the exported function:
// echo.tags = ['utility']
// echo.command = ['echo', /^say$/i]
// echo.customPrefix = /^[$#]/
// echo.owner = false; echo.rowner = false; echo.mods = false; echo.premium = false;
// echo.group = false; echo.private = false; echo.admin = false; echo.botAdmin = false;
// echo.premsub = false; echo.register = false; echo.disabled = false;
// echo.limit = 1; echo.exp = 17;
// echo.before = async (m, ctx) => { /*...*/ };
// echo.after  = async (m, ctx) => { /*...*/ };
// echo.all    = async (m, ctx) => { /* runs on every message */ };
```

Command matching (`handler.js`):
- `plugin.command` can be string | RegExp | (string|RegExp)[]
- Per-plugin `customPrefix` overrides global prefix for that plugin


## 9) Permissions, Limits, and Policies

Computed per message (`handler.js`):
- Roles
  - `isROwner`: real owner (from `global.owner` and the running bot JID)
  - `isOwner`: real owner OR message from self
  - `isMods`: owner or in `global.mods`
  - `isPrems`: owner or in `global.prems` or user.prem
- Group roles: `isAdmin`, `isBotAdmin` via group metadata

Plugin flags enforced:
- `rowner`, `owner`, `mods`, `premium`
- `group`, `private`, `admin`, `botAdmin`
- `premsub` – premium sub-bots check against `settings.actives`
- `register` – blocks if user not registered
- `limit` – consumes user limit if not premium (default user limit: 10)
- `disabled` – skip plugin entirely

Admin-only mode per chat:
- `global.db.data.chats[m.chat].modoadmin = true` → blocks commands from non-admins (unless owner)

Anti-bot / sub-bots:
- If `chat.antiLag === true`, only allowed bot JIDs in `chat.per` (plus the main bot) can run; others are ignored

Fail messages (`global.dfail`):
- Types: `rowner`, `owner`, `mods`, `premium`, `premsubs`, `group`, `private`, `admin`, `botAdmin`, `unreg`, `restrict`

Other policies:
- If `!opts['restrict']` and `plugin.tags?.includes('admin')`, that plugin is skipped
- Group allowlist hardcoded in `handler.js` (`gruposPermitidos`) allows only: `serbot`, `bots`, `kick`, `code`, `s`, `delsession`, `on`, `off`, `tutosub`

Limits & XP:
- `plugin.exp` (default 17) adds to `m.exp`
- `plugin.limit` consumes user limit; a notice is replied when used

Stats:
- Usage stats per plugin stored under `global.db.data.stats`


## 10) Optional Server Mode (Caveat)

`main.js` will load `./server.js` when run with `--server` and pass the WA connection + port (`PORT` env or 3000). However, `server.js` is not present in this repo.

Implications:
- `node index.js --server` will fail unless you provide `server.js`
- Dockerfile currently runs `CMD ["node", "index.js", "--server"]` and exposes port 5000, which will fail without `server.js`

Workarounds:
- Run without server: `node index.js` (or `npm start`)
- Or add your own `server.js` exporting a default function `(conn, port) => { /* start HTTP server */ }`
- Or change Dockerfile CMD to `node index.js`


## 11) Docker

Dockerfile summary:
- Base: `node:lts-buster`
- Installs: `ffmpeg`, `imagemagick`, `webp`
- Installs npm deps and `qrcode-terminal`
- Exposes `5000`
- CMD: `node index.js --server` (see §10)

Build:
```bash
docker build -t sasuke-bot .
```

Run (override CMD to avoid server mode):
```bash
# Persist sessions and db on host (Windows PowerShell example)
$pwd = (Get-Location).Path
docker run -it --name sasuke --rm \
  -v "$pwd/Sesiones:/app/Sesiones" \
  -v "$pwd/db:/app/db" \
  -v "$pwd/database.json:/app/database.json" \
  sasuke-bot node index.js
```

If you provide `server.js`, you may publish a port:
```bash
docker run -it --name sasuke -p 5000:5000 sasuke-bot
```


## 12) Troubleshooting

- __Invalid/closed session__: delete `Sesiones/Principal/creds.json`, then re-run and re-pair
- __QR not showing__: run `npm run qr`; ensure your terminal displays QR blocks
- __8‑digit code pairing__: run `npm run code`, enter phone with country code (e.g., `+521XXXXXXXXXX`)
- __FFmpeg/Imagick missing__: install and ensure binaries are in PATH
- __Plugins not loading__: fix loader path (see §8) or create `plugins/index/`
- __Server mode crash__: either add `server.js` or don’t use `--server` (see §10)


## 13) Development Notes

- Hot-reload of handler and plugins on change (see `main.js` and `index.js`)
- Console output filters certain base64 patterns (see `filterStrings` in `main.js`)
- Automatic cleanup of `tmp/` every ~3 minutes
- Periodic auto-reset (~45 min) via IPC to parent (`index.js` restarts child)


## 14) Contributing & Community

- Community/Channel links: see `README.md` and `config.js` (`global.group`, `global.canal`, `global.channel`)
- Keep API keys safe; don’t commit private keys to source control
- Submit plugins under `plugins/` following the metadata conventions in §8


## 15) Known Gaps / To‑Dos

- Plugin loader path mismatch: loader expects `plugins/index/`, repo uses `plugins/` root. Decide on one and align code or structure
- `server.js` missing while Docker CMD and `--server` flag expect it
- `cloudDBAdapter` referenced but not implemented; remote DB not enabled by default

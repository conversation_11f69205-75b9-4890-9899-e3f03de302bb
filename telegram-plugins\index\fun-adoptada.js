
let handler = async (m, { bot, text }) => {
    // Verificar si se ha proporcionado un usuario
    if (!text) {
        return bot.sendMessage(m.chat, { text: "Por favor, menciona a un usuario. Ejemplo: .adoptada @usuario" }, { quoted: m });
    }

    let userMentioned = text.split('@')[1]; // Extraer el ID del usuario mencionado

    // Obtener el nombre del usuario mencionado usando bot.getName()
    let mentionedName = await bot.getName(userMentioned + '');

    let adoptadaMessage = `*${mentionedName}* *ES/IS* *%* *ADOPTADA* _Sus padres se fueron x pañales 😞😂_`;

    // Enviamos el mensaje al chat
    await bot.sendMessage(m.chat,  text: adoptadaMessage );
}

handler.help = ['adoptada @usuario'];
handler.tags = ['diversión'];
handler.command = ['adoptada'];

export default handler;
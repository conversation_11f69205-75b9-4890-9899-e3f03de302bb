// import db from '../lib/database.js'

let handler = m => m
handler.before = async function (m, { bot, isAdmin, isBotAdmin }) {
if (!m.isGroup) return !1
let chat = global.db.data.chats[m.chat]
if (isBotAdmin && chat.autoRechazar) {
const prefixes = ['6', '90', '963', '966', '967', '249', '212', '92', '93', '94', '7', '49', '2', '91', '48']
if (prefixes.some(prefix => m.sender.startsWith(prefix))) {
await bot.groupRequestParticipantsUpdate(m.chat, [m.sender], 'reject')
}}}

export default handler
const handler = async (m, {bot, text, usedPrefix, command}) => {
  if (!text) throw `🤍 *No Se Encontró Ningun Prefijo, Por Favor Escriba Un Prefijo. Ejemplo:* ${usedPrefix + command} !`;
  global.prefix = new RegExp('^[' + (text || global.opts['prefix'] || '‎xzXZ/i!#$%+£¢€¥^°=¶∆×÷π√✓©®:;?&.\\-').replace(/[|\\{}()[\]^$+*?.\-\^]/g, '\\$&') + ']');
 // await m.reply(`*✅️ Prefijo Actualizado Con Éxito, Prefijo Actual: ${text}*`);
  bot.fakeReply(m.chat, `✅️ *Prefijo Actualizado Con Éxito, Prefijo Actual: ${text}*`, '0', '🤍 PREFIJO NUEVO 🤍')
};
handler.help = ['prefix'];
handler.tags = ['owner'];
handler.command = /^(prefix)$/i;
handler.rowner = true;
export default handler;
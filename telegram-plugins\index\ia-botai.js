
import fetch from 'node-fetch';

const handler = async (m, { bot, args }) => {
  if (!args[0]) {
    return m.reply('🚩 Ingresa un mensaje para que el bot responda.\n📌 Ejemplo: `.bota<PERSON>, ¿cómo estás?`');
  }

  const text = args.join(' ');
  const apiUrl = `https://api.nekorinn.my.id/ai/chatbot?ai_name=Barboza&text=${encodeURIComponent(text)}`;

  try {
    m.reply('🤖 Generando respuesta...');
    
    const response = await fetch(apiUrl);
    if (!response.ok) throw new Error(`Error en la API: ${response.statusText}`);

    const json = await response.json();
    if (json && json.data) {
      await bot.sendMessage(m.chat, { text: `🤖 *sᥲsᥙkᥱ ᑲ᥆𝗍 mძ 🌀 dice:* ${json.data}` }, { quoted: m });
    } else {
      await bot.sendMessage(m.chat,  text: "❌ No se obtuvo respuesta de la IA." );
    }
  } catch (error) {
    console.error('❌ Error en la solicitud:', error);
    m.reply('🚩 Ocurrió un error, intenta nuevamente más tarde.');
  }
};

handler.command = ['botai'];
export default handler;
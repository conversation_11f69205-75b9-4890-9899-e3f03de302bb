const handler = async (m, {bot, args, groupMetadata, participants, usedPrefix, command, isBotAdmin, isSuperAdmin}) => {
  if (!args[0]) return m.reply(`*🤍 Ingrese Algun Prefijo De Un Pais, 𝙴𝙹𝙴𝙼𝙿𝙻𝙾: ${usedPrefix + command} 52*`);
  if (isNaN(args[0])) return m.reply(`*🤍 Ingrese Algun Prefijo De Un Pais, 𝙴𝙹𝙴𝙼𝙿𝙻𝙾: ${usedPrefix + command} 52*`);
  const lol = args[0].replace(/[+]/g, '');
  const ps = (await bot.getGroupMetadata(m.chat)).admins => u.id).filter((v) => v !== bot.user.jid && v.startsWith(lol || lol));
  const bot = global.db.data.settings[bot.user.jid] || {};
  if (ps == '') return m.reply(`*🤍 Aqui No Hay Ningun Numero Con El Prefijo +${lol}*`);
  const numeros = ps.map((v)=> '🤍 @' + v.replace(/@.+/, ''));
  const delay = (time) => new Promise((res)=>setTimeout(res, time));
  switch (command) {
    case 'listanum': case 'listnum':
      bot.reply(m.chat, `*𝙻𝙸𝚂𝚃𝙰 𝙳𝙴 𝙽𝚄𝙼𝙴𝚁𝙾𝚂 𝙲𝙾𝙽 𝙴𝙻 𝙿𝚁𝙴𝙵𝙸𝙹𝙾 +${lol} 𝚀𝚄𝙴 𝙴𝚂𝚃𝙰𝙽 𝙴𝙽 𝙴𝚂𝚃𝙴 𝙶𝚁𝚄𝙿𝙾:*\n\n` + numeros.join`\n`, m, {mentions: ps});
      break;
    case 'kicknum':
      if (!bot.restrict) return m.reply('*¡Este Comando Esta Desabilitado Por El Propietario Del Bot!*');
      if (!isBotAdmin) return m.reply('*¡🤍 El Bot No Es Admin!*');
      bot.reply(m.chat, `*⏰️ Espere Iniciando La Eliminación*`, m);
      const ownerGroup = m.chat.split`-`[0] + '';
      const users = (await bot.getGroupMetadata(m.chat)).admins => u.id).filter((v) => v !== bot.user.jid && v.startsWith(lol || lol));
      for (const user of users) {
        const error = `@${user.split('@')[0]} ʏᴀ ʜᴀ sɪᴅᴏ ᴇʟɪᴍɪɴᴀᴅᴏ ᴏ ʜᴀ ᴀʙᴀɴᴅᴏɴᴀᴅᴏ ᴇʟ ɢʀᴜᴘᴏ*`;
        if (user !== ownerGroup + '' && user !== global.bot.user.jid && user !== global.owner + '' && user.startsWith(lol || lol) && user !== isSuperAdmin && isBotAdmin && bot.restrict) {
          await delay(2000);
          const responseb = await bot.groupParticipantsUpdate(m.chat, [user], 'remove');
          if (responseb[0].status === '404') m.reply(error, m.chat, {mentions: bot.parseMention(error)});
          await delay(10000);
        } else return m.reply('*🤍 𝙴𝚁𝚁𝙾𝚁*');
      }
      break;
  }
};
handler.command = /^(listanum|kicknum|listnum)$/i;
handler.group = handler.botAdmin = handler.admin = true;
handler.fail = null;
export default handler;
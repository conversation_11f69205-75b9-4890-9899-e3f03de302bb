const handler = async (m, {bot, participants, groupMetadata, args}) => {
  const pp = await bot.profilePictureUrl(m.chat, 'image').catch((_) => null) || './src/admins.jpg';
  const groupAdmins = participants.filter((p) => p.admin);
  const listAdmin = groupAdmins.map((v, i) => `${i + 1}. @${v.id.split('@')[0]}`).join('\n');
  const owner = groupMetadata.owner || groupAdmins.find((p) => p.admin === 'superadmin')?.id || m.chat.split`-`[0] + '';
  const pesan = args.join` `;
  const oi = `» ${pesan}`;
  const text = `『✦』Admins del grupo:

Mensaje ${oi}


${listAdmin}

『✦』Este comando solo si tienes problemas lo puede usar.`.trim();
  bot.sendDocument(m.chat, pp, 'error.jpg', text, m, false, {mentions: [...groupAdmins.map((v) => v.id), owner]});
};
handler.help = ['admins <texto>'];
handler.tags = ['grupo'];
// regex detect A word without case sensitive
handler.customPrefix = /a|@/i;
handler.command = /^(admins|@admins|dmins)$/i;
handler.group = true;
export default handler;
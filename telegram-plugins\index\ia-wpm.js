/**
  @ 🍀 Plugins WPW
  @ 🍀 Fuente: https://whatsapp.com/channel/0029VbBDTFd6mYPDtnetTK1f
  @ 🍀 Scrape: https://whatsapp.com/channel/0029VakezCJDp2Q68C61RH2C/3637
**/

import axios from 'axios'

const FILTROS = ['Coklat', 'Hitam', 'Nerd', 'Piggy', 'Carbon', 'Botak']

let yeon = async (m, { bot, text, usedPrefix, command }) => {
    const q = m.quoted || m
    const mime = (q.msg || q).mimetype || ''
    
    if (!/image/.test(mime)) {
        await bot.sendMessage(m.chat, { 
            react: { text: "❌", key: m.key } 
        })
        return bot.sendMessage(m.chat, {
            text: `✨ *Filtros Disponibles* ✨
▏ [ *Hitam:* Waifu Negra
▏ [ *Coklat:* Waifu Marrón
▏ [ *Nerd:* Waifu Nerd
▏ [ *Piggy:* Waifu Piggy
▏ [ *Carbon:* Waifu Carbon
▏ [ *Botak:* Waifu <PERSON>va

🌟 *Uso de la Función* 🌟
Responde / Envía una imagen con la descripción *"${usedPrefix + command} <tipo>"*. Ejemplo *"${usedPrefix + command} Hitam"*`
        })
    }

    try {
        await bot.sendMessage(m.chat, { 
            react: { text: "🎨", key: m.key } 
        })

        const buffer = await q.download()
        const base64Input = buffer.toString('base64')

        const args = text.split(' ')
        const filtroSeleccionado = args[0]?.toLowerCase() || 'hitam'
        const filtroValido = FILTROS.find(f => f.toLowerCase() === filtroSeleccionado)
        
        if (!filtroValido) {
            throw new Error(`Filtro no disponible. Elige entre: ${FILTROS.join(', ')}`)
        }

        const res = await axios.post('https://wpw.my.id/api/process-image ', {
            imageData: base64Input,
            filter: filtroValido.toLowerCase()
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Origin': 'https://wpw.my.id ',
                'Referer': 'https://wpw.my.id/ '
            }
        })

        const dataUrl = res.data?.processedImageUrl
        if (!dataUrl?.startsWith('data:image/')) {
            throw new Error('No se pudo procesar la imagen')
        }

        const base64Output = dataUrl.split(',')[1]
        const processedBuffer = Buffer.from(base64Output, 'base64')

        await bot.sendMessage(m.chat, {
            image: processedBuffer,
            caption: `✨ *Filtro aplicado con éxito* ✨
- - 🍀 *Filtro:* ${filtroValido}
- ⁠- 🔗 *Fuente:* https://wpw.my.id`
        })

        await bot.sendMessage(m.chat, { 
            react: { text: "✨", key: m.key } 
        })

    } catch (e) {
        console.error('Error:', e.message)
        await bot.sendMessage(m.chat, { 
            react: { text: "❌", key: m.key } 
        })
        await bot.sendMessage(m.chat, {
            text: `⚠️ *¡Uy, ocurrió un error, Senpai!* ${e.message || 'Esta función está experimentando problemas, inténtalo de nuevo más tarde 😅'}`
        })
    }
}

yeon.help = ['wpw <filtro>', 'penghitaman <filtro>']
yeon.tags = ['ai']
yeon.command = /^(wpw|penghitaman)$/i
yeon.register = true
yeon.limit = true
export default yeon

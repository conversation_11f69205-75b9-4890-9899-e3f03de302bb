import { igdl } from 'ruhend-scraper';

const handler = async (m, { text, bot, args, usedPrefix, command }) => {
  if (!args[0]) {
    return bot.reply(m.chat, '*\`Ingresa El link Del vídeo a descargar ❤️‍🔥\`*', m, fake);
  }

  await m.react('🕒');
  let res;
  try {
    res = await igdl(args[0]);
  } catch (error) {
    return bot.reply(m.chat, '*`Error al obtener datos. Verifica el enlace.`*', m);
  }

  let result = res.data;
  if (!result || result.length === 0) {
    return bot.reply(m.chat, '*`No se encontraron resultados.`*', m);
  }

  let data;
  try {
    data = result.find(i => i.resolution === "720p (HD)") || result.find(i => i.resolution === "360p (SD)");
  } catch (error) {
    return bot.reply(m.chat, '*`Error al procesar los datos.`*', m);
  }

  if (!data) {
    return bot.reply(m.chat, '*`No se encontró una resolución adecuada.`*', m);
  }

  await m.react('✅');
  let video = data.url;

  try {
    await bot.sendMessage(m.chat, { video: { url: video }, caption: dev, fileName: 'fb.mp4', mimetype: 'video/mp4' }, { quoted: m });
  } catch (error) {
    return bot.reply(m.chat, '*`Error al enviar el video.`*', m);
  await m.react('❌');
  }
};

handler.help = ['fb *<link>*'];
handler.estrellas = 2
handler.tags = ['downloader']
handler.command = /^(fb|facebook|fbdl)$/i;

export default handler;                                                                                                                                                                                                                                          
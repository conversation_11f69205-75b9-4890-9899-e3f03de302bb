[{"question": "¿Qué le dice un pingüino a otro pingüino?", "response": "Te quiero como a ningüino"}, {"question": "El que lo hace, no lo quiere; el que lo compra, no lo usa; el que lo usa, no lo sabe. ¿Qué es?", "response": "Un ataúd"}, {"question": "<PERSON><PERSON><PERSON> correr pero nunca caminar, tiene una boca pero nunca habla. ¿Qué es?", "response": "Un río"}, {"question": "Tiene llaves pero no puede abrir cerraduras. ¿Qué es?", "response": "Un piano"}, {"question": "Sube y baja, pero nunca se mueve. ¿Qué es?", "response": "Una escalera"}, {"question": "Soy una palabra de 7 letras, si me pronuncias, te dejo sin aire. Si me agregas una letra más, parec<PERSON>é bastante frío, pero si me quitas una, seré necesario. ¿Qué soy?", "response": "<PERSON><PERSON><PERSON>"}, {"question": "Entra en la casa por la ventana, pero nunca sale por la puerta. ¿Qué es?", "response": "La luz del sol"}, {"question": "Siempre estoy delante de ti, pero nunca puedes verme. ¿Qué soy?", "response": "El futuro"}, {"question": "<PERSON><PERSON><PERSON> puedes mantenerme hasta que me das. ¿Qué soy?", "response": "Tu palabra"}, {"question": "Soy un símbolo que siempre sigue a la R, pero nunca a la T. ¿Qué soy?", "response": "La letra E"}, {"question": "Cuanto más te quito, más grande me vuelvo. ¿Qué soy?", "response": "Un agujero"}, {"question": "Tengo ciudad<PERSON>, pero no edificios. Tengo selvas, pero no árboles. Tengo ríos, pero no agua. ¿Qué soy?", "response": "Un mapa"}, {"question": "<PERSON><PERSON><PERSON> ll<PERSON>, pero no puede abrir ninguna puerta. A veces tiene números, pero nunca hace cálculos. ¿Qué es?", "response": "Un teclado"}, {"question": "Soy un lugar donde todos corren, pero nadie se mueve. <PERSON>em<PERSON><PERSON> estoy lleno, pero nunca hay multitud. ¿Qué soy?", "response": "Un sueño"}, {"question": "Soy el comienzo de la sabiduría, el final de la vida y el medio de la oscuridad. ¿Qué soy?", "response": "La letra E"}, {"question": "Soy un número que siempre está en medio de 5 y 9. ¿Qué soy?", "response": "El número 7"}, {"question": "Soy una palabra de 12 letras, aunque si me pronuncias, tendrás una de las 12 partes. Si me escribes, tendrás todas las 12. ¿Qué soy?", "response": "El alfabeto"}, {"question": "Tengo ojos, pero nunca lloro. Tengo un corazón, pero nunca amo. ¿Qué soy?", "response": "Un espantapájaros"}, {"question": "Soy un agujero que puedes llenar con objetos, pero si lo llenas con agua, desapareceré. ¿Qué soy?", "response": "Un agujero negro"}, {"question": "Tengo un cuello pero no tengo cabeza, y te acompaño en la cama todas las noches. ¿Qué soy?", "response": "<PERSON> almohada"}, {"question": "Soy un lugar donde las personas van cuando están rotas, pero nunca necesito ser reparado. ¿Qué soy?", "response": "Un espejo"}, {"question": "Tengo hojas, pero no soy un árbol. Tengo una cara, pero no soy una persona. ¿Qué soy?", "response": "Un libro"}, {"question": "Soy un camino que puedes caminar, pero siempre estás retrocediendo. ¿Qué soy?", "response": "Una escalera"}, {"question": "<PERSON><PERSON><PERSON> puedes to<PERSON>lo, pero siempre está frente a ti. ¿Qué es?", "response": "El futuro"}, {"question": "Tengo un ojo pero no puedo ver. ¿Qué soy?", "response": "<PERSON> aguja"}, {"question": "Siempre estoy en el agua, pero nunca me mojo. ¿Qué soy?", "response": "Una sombra"}, {"question": "Puedo volar sin alas, siempre estoy cerca del suelo, pero nunca me caigo. ¿Qué soy?", "response": "Un sueño"}, {"question": "Soy redondo como una moneda, pero no tengo valor. ¿Qué soy?", "response": "Un botón"}, {"question": "Soy una palabra de cinco letras y me temen más que a la muerte. Si me quitas la primera letra, tendrás un lugar donde la gente vive. ¿Qué soy?", "response": "<PERSON><PERSON><PERSON><PERSON>"}, {"question": "Soy un lugar donde nunca se puede entrar, pero siempre puedes ver lo que está dentro. ¿Qué soy?", "response": "Un ojo"}, {"question": "<PERSON>go dientes, pero no puedo masticar. ¿Qué soy?", "response": "Un peine"}, {"question": "Soy un agujero en el agua que puedes llenar con una taza. ¿Qué soy?", "response": "Un agujero de llave"}, {"question": "Tengo una cabeza, un cuerpo, pero no tengo alma. ¿Qué soy?", "response": "Un muñeco"}, {"question": "Tengo manos y una cara, pero no tengo cuerpo. ¿Qué soy?", "response": "Un reloj"}, {"question": "Soy una cosa que es más útil cuando está rota. ¿Qué soy?", "response": "Un huevo"}, {"question": "Soy un lugar donde puedes encontrar tesoros y libros, pero nunca encontrarás oro ni palabras. ¿Qué soy?", "response": "Una biblioteca"}, {"question": "Si me nombras, me rompes. ¿Qué soy?", "response": "El silencio"}, {"question": "Soy un número sin fin, pero siempre empiezo con un 1. ¿Qué soy?", "response": "El número Pi"}, {"question": "¿Qué le dice un ciego a otro ciego?", "response": "Después nos vemos"}, {"question": "¿En qué se parece un alfiler a un policía?", "response": "En que los dos prenden"}, {"question": "¿Qué se congela cuando se calienta?", "response": "La computadora"}, {"question": "¿Qué fruta tiene muchos ojos, pero no puede ver?", "response": "La piña"}, {"question": "¿En qué idioma se comunican las tortugas?", "response": "En tortugués"}, {"question": "¿Cómo llama un policía sevillano a su pistola?", "response": "Mi arma"}, {"question": "¿Qué es todo para uno y nada para lo demás?", "response": "La mente"}, {"question": "¿Cuál es el colmo de una aspiradora?", "response": "Ser alérgica al polvo"}, {"question": "¿Qué es aquello que si nombras, desaparece?", "response": "El silencio"}, {"question": "¿Qué necesitamos para escribir durmiendo?", "response": "Estar despiertos"}, {"question": "¿Qué objeto puede tener cara sin poseer cuerpo?", "response": "<PERSON> moneda"}, {"question": "Ali<PERSON><PERSON> la casa, <PERSON><PERSON><PERSON><PERSON> con hielo", "response": "La nevera"}, {"question": "¿Qué tienen las mujeres en medio de las piernas?", "response": "Las rodillas"}, {"question": "Entra duro y seco y sale blandito y mojado ¿Qué es?", "response": "Un chicle"}, {"question": "Estoy en todo y estoy en nada ¿qué soy?", "response": "La letra d"}, {"question": "Continuas en rojo, pero te paras en verde. ¿Qué soy?", "response": "<PERSON>"}, {"question": "¿Qué es negro cuando lo compras, rojo cuando lo usas, y gris cuando lo tiras?", "response": "El carbón"}, {"question": "¿Qué hay delante de ti siempre pero que no se puede ver?", "response": "El futuro"}, {"question": "¿Qué mes tiene 28 días?", "response": "Todos"}, {"question": "¿Cómo puede estar alguien sin dormir diez días y no tener sueño?", "response": "Durmiendo por las noches"}, {"question": "¿De qué llenarías un barril para que pese lo menos posible?", "response": "De agujer<PERSON>"}, {"question": "Si durante una carrera adelantas a quien va segundo, ¿en qué posición estás?", "response": "<PERSON><PERSON><PERSON>"}, {"question": "¿En qué lugar el jueves va antes que el miércoles?", "response": "En el diccionario"}, {"question": "¿Qué sube y baja pero se queda siempre en el mismo sitio?", "response": "Las escaleras"}, {"question": "¿Cómo llaman a un ascensor en China?", "response": "Apretando el botón"}, {"question": "¿Qué es lo primero que hace un elefante al salir el sol?", "response": "Sombra"}, {"question": "¿Por qué un Tiranosaurus Rex no puede aplaudir?", "response": "Porque se extinguió"}, {"question": "¿Qué animal siempre está lleno?", "response": "La ballena"}, {"question": "¿Qué es lo primero que todos hacemos al despertar?", "response": "Abrir los ojos"}, {"question": "¿Cuál es el número que si le quitas la mitad, vale 0?", "response": "El ocho"}, {"question": "¿Qué se necesita para encender una vela?", "response": "Que esté apagada"}, {"question": "¿Cuál es el país donde termina todo?", "response": "Finlandia"}, {"question": "La respuesta a esta pregunta es #######", "response": "#######"}, {"question": "Cuál es el final de todo", "response": "La letra o"}, {"question": "¿Dónde se encuentra una pared con otra pared?", "response": "En la esquina"}, {"question": "¿Qué tiene muchas palabras, pero nunca habla?", "response": "Un libro"}, {"question": "¿Qué cosa te pertenece, pero los demás usan más que tú?", "response": "Tu nombre"}, {"question": "¿En qué momento una persona se convierte en fruta?", "response": "<PERSON>uando espera"}, {"question": "Los padres de José tienen tres hijos: <PERSON>, <PERSON><PERSON>, ¿cómo se llama el tercero?", "response": "<PERSON>"}, {"question": "¿Qué palabra del diccionario se escribe incorrectamente?", "response": "Incorrectamente"}, {"question": "<PERSON><PERSON><PERSON><PERSON> por dentro, con pelos por fuera. Comienza por la C. ¿De qué se trata?", "response": "Un coco"}, {"question": "<PERSON> y Fabián están jugando al ajedrez. Llevan 5 partidas, pero ambos han ganado 3. ¿Cómo es posible?", "response": "Porque juegan con más personas"}, {"question": "¿Cuántos 9 hay entre el 1 y el 100?", "response": "20"}, {"question": "Son dos abanicos que están todo el día sin parar, pero cuando te duermas se paran y quietos se quedarán", "response": "Las pestañas"}, {"question": "Oro parece, plata no es y el que no lo adivine un tonto es", "response": "Un platano"}, {"question": "Círculo bien redondo al que si le pegas, das un brinco del susto", "response": "Un tambor"}, {"question": "Un caballo blanco entró en el Mar Negro. ¿Cómo salió?", "response": "<PERSON><PERSON><PERSON> m<PERSON>"}, {"question": "Si un tren eléctrico va de Norte a Sur, ¿Hacia qué lado echará el humo?", "response": "Es eléctrico, no echa humo"}, {"question": "Tiene 4 letras, empieza por C y termina por O y esta en la parte trasera", "response": "El codo"}, {"question": "Se estrella un avión y mueren todos, justo en la frontera entre España y Portugal ¿A que Hospital tienen que llevar a los supervivientes? ", "response": "No hay supervivientes"}, {"question": "Este banco está ocupado por un padre y por un hijo, el padre se llama Juan y el hijo ya te lo he dicho", "response": "Esteban"}, {"question": "Algunos meses tienen 31 días, <PERSON><PERSON><PERSON> solo 30.¿Cuantos tienen 28 días?", "response": "Todos"}, {"question": "¿Que es lo que puedes encontrar una vez en un minutos, dos veces en un momento y ninguna vez cien años", "response": "La letra M"}, {"question": "¿Que llanta no gira a la derecha?", "response": "las llanta de respuesto"}, {"question": "la gente me compra para comer pero nunca me come ¿que soy?", "response": "un plato"}, {"question": "Matamos y alentamos tu decides si somos un venemos o un dulce ¿que somos?", "response": "las palabras"}, {"question": "¿con que manos es mejor revolver el caldo?", "response": "es mejor usar una cuchara"}, {"question": "¿quien se moja mientras seca?", "response": "la toalla"}, {"question": "Cuantos mas le quitas, mas grande se vuelve. ¿que es?", "response": "un foso"}, {"question": "¿Que palabra es p_ta?", "response": "pata"}, {"question": "tengo un par, van colgando y al camina se van desplazando ¿que son?", "response": "los brazos"}, {"question": "¿Que es largo y le cuelga a los hombres por delante?", "response": "la corbata"}, {"question": "¿Cual es el juguete más egoísta?", "response": "El Yo-Yo"}, {"question": "¿Que hacer una persona corriendo rápidamente alrededor de la universidad?", "response": "Su carrera universitaria"}, {"question": "No son flores, pero tienes plantas y olores ¿que es?", "response": "Los pies"}, {"question": "No lo puedes ver, pero no puedes vivir sin él. ¿Qué es?", "response": "El aire"}]
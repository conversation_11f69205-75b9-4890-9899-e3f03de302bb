
const timeout = 60000; // 60 segundos para resolver el acertijo

const handler = async (m, { bot}) => {
    const acertijos = [
        {
            descripcion: "🔐 *Estás atrapado en una habitación cerrada.* Hay una mesa con tres objetos: un libro, una vela y un espejo. Uno de ellos tiene una pista para la salida.",
            opciones: ["Revisar el libro", "Encender la vela", "Mirar el espejo"],
            respuestaCorrecta: 2,
            resultado: ["❌ No encuentras nada útil en el libro.", "❌ La vela solo ilumina, pero no revela pistas.", "✅ En el espejo aparece un código secreto que desbloquea la puerta."]
},
        {
            descripcion: "🚪 *La puerta está bloqueada con un número misterioso.* En la pared hay una secuencia: 2, 4, 8, 16,??",
            opciones: ["32", "20", "64"],
            respuestaCorrecta: 0,
            resultado: ["✅ La secuencia sigue duplicándose: 32 es la clave correcta.", "❌ 20 no tiene sentido con la progresión.", "❌ 64 es demasiado alto."]
},
        {
            descripcion: "🕵️‍♂️ *Hay un cuadro en la habitación con una firma sospechosa.* Un papel en el suelo dice 'La clave está en el arte'.",
            opciones: ["Examinar el marco", "Leer la firma del cuadro", "Romper el cuadro"],
            respuestaCorrecta: 1,
            resultado: ["❌ El marco está vacío.", "✅ La firma del artista contiene un código para desbloquear la salida.", "❌ Romper el cuadro solo deja trozos por todas partes."]
}
    ];

    const acertijoSeleccionado = acertijos[Math.floor(Math.random() * acertijos.length)];

    let mensaje = `🚪 *Escape Room Virtual* 🚪\n\n📜 *Escenario:* ${acertijoSeleccionado.descripcion}\n\n`;
    acertijoSeleccionado.opciones.forEach((opcion, i) => {
        mensaje += `🔹 ${i + 1}. ${opcion}\n`;
});

    mensaje += "\n📌 *Responde con el número de la opción correcta antes de que el tiempo se acabe!*";

    bot.escapeGame = bot.escapeGame || {};
    bot.escapeGame[m.chat] = {
        respuestaCorrecta: acertijoSeleccionado.respuestaCorrecta,
        resultado: acertijoSeleccionado.resultado,
        timeout: setTimeout(() => {
            if (bot.escapeGame[m.chat]) {
                bot.reply(m.chat, "⏰ *Tiempo agotado!* No lograste escapar a tiempo.", m);
                delete bot.escapeGame[m.chat];
}
}, timeout)
};

    await bot.sendMessage(m.chat, { text: mensaje});
};

handler.before = async (m, { bot}) => {
    if (bot.escapeGame && bot.escapeGame[m.chat]) {
        const respuesta = parseInt(m.text.trim());
        const { respuestaCorrecta, resultado} = bot.escapeGame[m.chat];

        if (respuesta>= 1 && respuesta <= resultado.length) {
            delete bot.escapeGame[m.chat];
            return bot.reply(m.chat, resultado[respuesta - 1], m);
} else {
            return bot.reply(m.chat, `❌ *Opción no válida. Intenta con un número entre 1 y ${resultado.length}.*`, m);
}
}
};

handler.command = ["escape"];
export default handler;
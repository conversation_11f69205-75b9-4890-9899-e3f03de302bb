const _0x7a8c21 = _0x487c;
(function (_0x3de422, _0x346f22) {
    const _0x5e4f78 = _0x487c, _0x345904 = _0x3de422();
    while (!![]) {
        try {
            const _0xe66261 = -parseInt(_0x5e4f78(0x1bb)) / (0x478 + 0x4 * 0x994 + -0x2ac7) * (parseInt(_0x5e4f78(0x1be)) / (0x1c45 + 0x1 * -0x1591 + -0x359 * 0x2)) + parseInt(_0x5e4f78(0x1db)) / (0x2 * 0x12eb + 0x7 * 0x101 + -0x2cda) * (parseInt(_0x5e4f78(0x1e6)) / (-0xc98 + -0x7 * 0x239 + 0x1c2b)) + -parseInt(_0x5e4f78(0x1d6)) / (0x2 * -0x1a0 + 0x11f3 * 0x1 + 0x757 * -0x2) + -parseInt(_0x5e4f78(0x1ca)) / (0x12fa + 0xcf5 * 0x3 + -0x39d3) * (-parseInt(_0x5e4f78(0x1c1)) / (0x201e + -0xdd4 + 0xbb * -0x19)) + parseInt(_0x5e4f78(0x1de)) / (-0x1e3a + 0x3 * 0x5ea + 0xc84) + parseInt(_0x5e4f78(0x1d5)) / (-0xda * 0x1b + 0x1c57 + -0x1 * 0x550) + parseInt(_0x5e4f78(0x1df)) / (-0x45 * -0x7 + -0x212a + 0x1f51);
            if (_0xe66261 === _0x346f22)
                break;
            else
                _0x345904['push'](_0x345904['shift']());
        } catch (_0x4ce1fa) {
            _0x345904['push'](_0x345904['shift']());
        }
    }
}(_0x5309, 0x2e78 + -0x1 * 0xd1df + 0x23848));
function _0x5309() {
    const _0x4be262 = [
        'Fdkpm',
        'vswhJ',
        'url',
        'tags',
        'iAxoH',
        'iGjtN',
        '60149MvgfEm',
        'image',
        'hd2',
        '2aeeQFN',
        'inteligencia_artificial',
        'message',
        '1141EfByIp',
        'lrKFc',
        'mediaType',
        'Mozilla/5.0\x20(Linux;\x20Android\x2010;\x20K)\x20AppleWebKit/537.36\x20(KHTML,\x20como\x20Gecko)\x20Chrome/*********\x20Mobile\x20Safari/537.36',
        'user_id',
        '⏳\x20Procesando\x20imagen\x20en\x20HD...',
        'https://picupscaler.com/',
        'sendMessage',
        'true',
        '1176sfFxsa',
        '❌\x20No\x20se\x20pudo\x20descargar\x20la\x20imagen',
        '*/*',
        'append',
        'hd\x20<responde\x20a\x20imagen>',
        'image_url',
        'undefined',
        'UKuuf',
        'https://picupscaler.com/api/generate/handle',
        'qOdbT',
        'get',
        '755541TzGCub',
        '545215ePTajJ',
        'quoted',
        'download',
        'nytol',
        'image/jpeg',
        '264fievcE',
        'mimetype',
        'help',
        '11296RdBaPd',
        '392840OyVWAz',
        '❌\x20Error\x20al\x20mejorar\x20la\x20imagen',
        '⚠️\x20Responde\x20a\x20una\x20imagen\x20con\x20el\x20comando:.hd',
        'ApXwb',
        'HnyJS',
        'msg',
        'imagen.jpg',
        '5284lmrwKa',
        'CJbUC',
        'https://picupscaler.com',
        'reply',
        '✅\x20Imagen\x20mejorada\x20con\x20éxito'
    ];
    _0x5309 = function () {
        return _0x4be262;
    };
    return _0x5309();
}
import _0x45f6f8 from 'axios';
function _0x487c(_0x4de860, _0x42ca49) {
    const _0x5b7edd = _0x5309();
    return _0x487c = function (_0x389084, _0x35abb7) {
        _0x389084 = _0x389084 - (0x2669 * 0x1 + -0x56d * -0x2 + -0x2f92);
        let _0x242ddf = _0x5b7edd[_0x389084];
        return _0x242ddf;
    }, _0x487c(_0x4de860, _0x42ca49);
}
import _0x10c5dd from 'form-data';
let handler = async (_0x4a09e4, {conn: _0x18b7f3}) => {
    const _0x385be7 = _0x487c, _0x276579 = {
            'HjPSL': _0x385be7(0x1c6),
            'iGjtN': _0x385be7(0x1e1),
            'lrKFc': _0x385be7(0x1cb),
            'HnyJS': function (_0x1b0eac, _0x448da9) {
                return _0x1b0eac(_0x448da9);
            },
            'ApXwb': _0x385be7(0x1b4)
        };
    _0x4a09e4[_0x385be7(0x1b3)](_0x276579['HjPSL']);
    try {
        let _0x2daeba = _0x4a09e4[_0x385be7(0x1d7)] || _0x4a09e4, _0x26b07f = (_0x2daeba[_0x385be7(0x1e4)] || _0x2daeba)[_0x385be7(0x1dc)] || _0x2daeba[_0x385be7(0x1dc)] || _0x2daeba[_0x385be7(0x1c3)] || '';
        if (!_0x26b07f)
            throw _0x276579[_0x385be7(0x1ba)];
        if (!/image\/(jpe?g|png)/['test'](_0x26b07f))
            throw '⚠️\x20El\x20formato\x20' + _0x26b07f + '\x20no\x20está\x20soportado';
        let _0x2daadd = await _0x2daeba[_0x385be7(0x1d8)]?.();
        if (!_0x2daadd)
            throw _0x276579[_0x385be7(0x1c2)];
        let _0x5dfc0f = await _0x276579[_0x385be7(0x1e3)](mejorarImagen, _0x2daadd);
        await _0x18b7f3[_0x385be7(0x1c8)](_0x4a09e4['chat'], {
            'image': _0x5dfc0f,
            'caption': _0x276579[_0x385be7(0x1e2)]
        }, { 'quoted': _0x4a09e4 });
    } catch (_0x45bae6) {
        _0x4a09e4[_0x385be7(0x1b3)]('❌\x20Error:\x20' + _0x45bae6[_0x385be7(0x1c0)]);
    }
};
handler[_0x7a8c21(0x1dd)] = [_0x7a8c21(0x1ce)], handler[_0x7a8c21(0x1b8)] = [_0x7a8c21(0x1bf)], handler['command'] = [_0x7a8c21(0x1bd)];
export default handler;
async function mejorarImagen(_0x3c2c51) {
    const _0x54b2be = _0x7a8c21, _0x4c62d8 = {
            'nENVw': _0x54b2be(0x1bc),
            'EowSY': _0x54b2be(0x1e5),
            'kbqmm': _0x54b2be(0x1da),
            'CJbUC': _0x54b2be(0x1d0),
            'qOdbT': 'is_public',
            'XVnmL': _0x54b2be(0x1cc),
            'vswhJ': _0x54b2be(0x1c7),
            'iAxoH': _0x54b2be(0x1c4),
            'RoWOH': _0x54b2be(0x1d2),
            'nytol': _0x54b2be(0x1e0),
            'UKuuf': function (_0x248a7b, _0x491e56) {
                return _0x248a7b === _0x491e56;
            },
            'Fdkpm': 'string'
        };
    try {
        const _0x4dde92 = new _0x10c5dd();
        _0x4dde92[_0x54b2be(0x1cd)](_0x4c62d8['nENVw'], _0x3c2c51, {
            'filename': _0x4c62d8['EowSY'],
            'contentType': _0x4c62d8['kbqmm']
        }), _0x4dde92[_0x54b2be(0x1cd)](_0x54b2be(0x1c5), _0x4c62d8[_0x54b2be(0x1b1)]), _0x4dde92[_0x54b2be(0x1cd)](_0x4c62d8[_0x54b2be(0x1d3)], _0x54b2be(0x1c9));
        const _0x100923 = {
                ..._0x4dde92['getHeaders'](),
                'Accept': _0x4c62d8['XVnmL'],
                'Origin': _0x54b2be(0x1b2),
                'Referer': _0x4c62d8[_0x54b2be(0x1b6)],
                'User-Agent': _0x4c62d8[_0x54b2be(0x1b9)]
            }, {data: _0x1dfe4a} = await _0x45f6f8['post'](_0x4c62d8['RoWOH'], _0x4dde92, { 'headers': _0x100923 }), _0x3b82f9 = _0x1dfe4a?.[_0x54b2be(0x1cf)] || _0x1dfe4a?.[_0x54b2be(0x1b7)];
        if (!_0x3b82f9)
            throw _0x4c62d8[_0x54b2be(0x1d9)];
        const _0x144d69 = await _0x45f6f8[_0x54b2be(0x1d4)](_0x3b82f9, { 'responseType': 'arraybuffer' });
        return Buffer['from'](_0x144d69['data']);
    } catch (_0x10de2c) {
        throw '❌\x20Error\x20en\x20mejora:\x20' + (_0x4c62d8[_0x54b2be(0x1d1)](typeof _0x10de2c, _0x4c62d8[_0x54b2be(0x1b5)]) ? _0x10de2c : _0x10de2c[_0x54b2be(0x1c0)]);
    }
}
import { igdl } from "ruhend-scraper";

let handler = async (m, { args, bot }) => { 
    if (!args[0]) {
        return bot.reply(m.chat, '*\`Ingresa El link Del vídeo a descargar 🤍\`*', m, fake);
    }

    try {
        await m.react('🕑');

        let res = await igdl(args[0]);
        let data = res.data; 

        for (let media of data) {
            await new Promise(resolve => setTimeout(resolve, 2000));

            await m.react('✅');
            await bot.sendDocument(m.chat, media.url, 'instagram.mp4', dev, null, m); 
        }
    } catch {
        await m.react('❌');
    }
}

handler.corazones = 2
handler.command = ['ig', 'igdl', 'instagram'];
handler.tags = ['dl'];
handler.help = ['ig *<link>*'];

export default handler;
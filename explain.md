# How to Run Sasuke-bot-V2 for Free (Beginner Friendly)

This guide explains, step by step, how to get your WhatsApp bot running for free. It focuses on simple, reliable ways that do not require coding experience.

Important facts about WhatsApp bots:
- The bot must stay online to receive/send messages (a background process that keeps a WebSocket connection open).
- “Free hosting” services often sleep after a period of inactivity. For a WhatsApp bot, the most reliable free options are running on your own device (Windows PC or Android with Termux). A free cloud VM is possible but setup takes longer and availability varies.

Contents:
1) Required fixes in this codebase (1 minute)
2) Option A: Run on your Windows PC (free)
3) Option B: Run on an Android phone with Termux (free, always-on if you keep the phone on)
4) Option C: Free cloud VM (Oracle Cloud Free Tier – optional, availability varies)
5) Pairing your WhatsApp (QR or 8‑digit code)
6) Troubleshooting
7) FAQ & best practices


## 1) Required fixes in this repo (do this once)

There are two small issues you should be aware of:

- Plugin loader path: `main.js` currently loads plugins from `plugins/index/`, but your plugins are in `plugins/`.
  - Fix: open `main.js` and change this line:
    - From: `const pluginFolder = global.__dirname(join(__dirname, './plugins/index'));`
    - To:   `const pluginFolder = global.__dirname(join(__dirname, './plugins'));`

- Optional server mode: `--server` expects a `server.js` file, which this repo does not include.
  - Fix: do not use `--server` when starting the bot. If you use Docker later, run `node index.js` instead of `node index.js --server`.

That’s it. The bot will run fine without server mode.


## 2) Option A: Run on your Windows PC (no cost)

This is the easiest way to get started.

1. Install Node.js (LTS)
   - Go to https://nodejs.org and install the LTS version.
   - During installation, keep defaults.

2. Install FFmpeg and ImageMagick
   - FFmpeg: https://www.gyan.dev/ffmpeg/builds/ (download release, add bin folder to PATH)
   - ImageMagick: https://imagemagick.org (enable legacy utilities during install)
   - These are used for media features. The bot can start without them but some commands may fail.

3. Open this project folder in PowerShell
   - Right‑click the folder and choose “Open in Terminal” (or open PowerShell and navigate to the folder).

4. Install dependencies
```powershell
npm install
```

5. First run and pair with WhatsApp
- QR method (shows QR in your terminal):
```powershell
npm run qr
```
- 8‑digit code (useful if QR is not visible):
```powershell
npm run code
```
Follow the prompt to enter your phone number with country code (e.g., +521XXXXXXXXXX). A pairing code will be printed.

6. Start the bot normally
```powershell
npm start
```
Keep this window open. If you close it or shut down the PC, the bot goes offline.

Optional keep‑alive on Windows:
- Use Task Scheduler to auto‑start at login:
  - Create a Basic Task → Trigger: At log on → Action: Start a Program
  - Program/script: `node`
  - Add arguments: `index.js`
  - Start in: Full path to this project folder


## 3) Option B: Run on an Android phone with Termux (free)

This keeps the bot online using your phone (good if you have a spare phone).

1. Install Termux
- Recommended source: https://f-droid.org (search Termux) or the official Termux GitHub page. Avoid outdated Play Store versions.

2. Initial setup in Termux
```bash
pkg update -y && pkg upgrade -y
pkg install -y nodejs-lts git ffmpeg imagemagick
termux-setup-storage  # grant storage access if you want to place the project in shared storage
```

3. Get the project into Termux
- If you have the code locally, copy it into `~/storage/shared/` and then move it into Termux home.
- Or use git to clone your repo fork:
```bash
git clone <your-repo-url>
cd Sasuke-bot-V2-main
```

4. Install dependencies and fix plugin path (see §1)
```bash
npm install
# edit main.js to change './plugins/index' -> './plugins' (see §1)
```

5. Pair & run
```bash
npm run qr       # or: npm run code
npm start
```

Keep awake tips:
- Run a wakelock so Android doesn’t suspend Termux:
```bash
termux-wake-lock
```
- Optional auto‑start on boot: install the Termux:Boot app and place a script in `~/.termux/boot/` that runs `npm start` inside the project folder.


## 4) Option C: Free Cloud VM (Oracle Cloud Always Free)

Availability varies by region and may require a credit card for verification. If you obtain a free VM, the bot can run 24/7.

High‑level steps on Ubuntu/Debian VM:
```bash
# SSH into your VM first
sudo apt update && sudo apt install -y curl ffmpeg imagemagick git
# Install Node.js LTS (example using NodeSource)
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt install -y nodejs

# Get your project
git clone <your-repo-url>
cd Sasuke-bot-V2-main

# Fix plugin path, then install
nano main.js   # change './plugins/index' -> './plugins'
npm install

# Pair and run
npm run code    # easier over SSH (enter your phone number, get 8-digit code)
npm start
```
Keep it running with a process manager:
```bash
sudo npm i -g pm2
pm2 start index.js --name sasuke
pm2 save
pm2 startup   # follow the printed instructions so it restarts on reboot
```


## 5) Pairing your WhatsApp (first run)

Two supported methods:
- QR method: `npm run qr`
  - Your terminal prints a QR code. Open WhatsApp → Linked devices → Link a device → Scan QR.
- 8‑digit code: `npm run code`
  - Enter your phone number when prompted. The bot prints a code like `1234-5678`. In WhatsApp, link a device using pairing code and type the code.

Sessions are stored in `Sesiones/Principal/`. Keep this folder safe; it contains your login credentials.


## 6) Troubleshooting

- Command not responding:
  - Check the console for errors.
  - Make sure you fixed the plugin folder path (see §1).
  - Some groups are hard‑restricted in `handler.js` (`gruposPermitidos`). Only certain commands work in those groups.

- Media features failing:
  - Install and add to PATH: `ffmpeg`, `imagemagick`, `webp`.

- Session issues:
  - Delete `Sesiones/Principal/creds.json` and pair again.

- Docker users:
  - The Dockerfile runs `node index.js --server`, but `server.js` is missing. Run the image with `node index.js` instead, or add your own `server.js`.


## 7) FAQ & Best Practices

- Can I close my terminal after starting the bot?
  - No. If the terminal/process stops, the bot disconnects. Use PM2 (Linux) or Task Scheduler (Windows) to keep it alive.

- Is a free cloud VM truly free?
  - Some providers offer always‑free tiers but availability varies and usually requires verification. Running on your own device (Windows/Android) is the simplest free option.

- Is server mode required?
  - No. The bot runs fine without `--server`. Only use server mode if you add a `server.js` that starts an HTTP server.

- Where are configs?
  - `config.js` (owners, branding, API keys, session paths). Avoid committing private keys.

- Where are plugins?
  - `plugins/`. Each plugin is a `.js` file. The handler enforces permissions like owner/mod/premium, group/admin, limits, etc.


You’re done! Start with Option A (Windows) or Option B (Termux). If you later want 24/7 uptime, consider Option C (free VM) or a low‑cost VPS.

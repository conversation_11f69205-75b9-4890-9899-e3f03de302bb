
let handler = async (m, { bot, text, participants }) => {
let users = (await bot.getGroupMetadata(m.chat)).admins)
let q = m.quoted ? m.quoted : m
let c = m.quoted ? m.quoted : m.msg
const msg = bot.cMod(m.chat,
generateWAMessageFromContent(m.chat, {
[c.toJSON ? q.mtype : 'extendedTextMessage']: c.toJSON ? c.toJSON() : {
text: c || ''
}
}, {
quoted: m,{}
userJid: bot.user.id
}),
text || q.text, bot.user.jid, { mentions: users }
)
await bot.relayMessage(m.chat, msg.message, { messageId: msg.key.id })
}
handler.help = ['hidetag']
handler.tags = ['group']
handler.command = ['hidetag', 'notify','n','noti'] 
handler.group = true
export default handler
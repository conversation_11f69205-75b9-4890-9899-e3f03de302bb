var handler = async (m, { bot,usedPrefix, command, text }) => {

    var done = '✅'; 

    if (isNaN(text) && !text.match(/@/g)){
        
    } else if (isNaN(text)) {
        var number = text.split`@`[1];
    } else if (!isNaN(text)) {
        var number = text;
    }

    if (!text && !m.quoted) return bot.reply(m.chat, `*<PERSON>bes responder a un mensaje de aquel usuario cual le darás admin.*`, m,{});
    
    if (number && (number.length > 13 || (number.length < 11 && number.length > 0))) return bot.reply(m.chat, `*<PERSON>bes responder a un mensaje de aquel usuario cual le darás admin.*`, m,{});

    try {
        var user;
        if (text) {
            user = number + '';
        } else if (m.quoted && m.quoted.sender) { 
            user = m.quoted.sender;
        } else if (m.mentionedJid && m.mentionedJid[0]) { 
            user = m.mentionedJid[0]; 
        } 
    } catch (e) {
        console.error("Error determining user:", e); 
        return bot.reply(m.chat, `Ocurrió un error al identificar al usuario.`, m);
    } finally {
        if (user) { 
            await bot.groupParticipantsUpdate(m.chat, [user], 'promote');
            bot.reply(m.chat, `${done} Fue agregado como admin del grupo con éxito.`, m);
        } else {
            bot.reply(m.chat, `No se pudo identificar a ningún usuario válido para promover.`, m);
        }
    }
}

handler.help = ['promote'];
handler.tags = ['grupo'];
handler.command = ['promote','darpija', 'promover']; 
handler.group = true;
handler.admin = true;
handler.botAdmin = true;
handler.fail = null;

export default handler;

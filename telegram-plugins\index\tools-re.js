const _0xb2dfd = _0x4e9f;
function _0x2adc() {
    const _0x206b88 = [
        'load',
        '3329082qvZwWh',
        'tr:nth-child(2)\x20td:nth-child(2)\x20b',
        '⚠️\x20Error:\x20',
        'LAutg',
        'mediafiresearch',
        '10TZAmUg',
        'match',
        'trim',
        'length',
        '1503468GIztrP',
        'random',
        'all',
        'text',
        'Contoh:.mfsearch\x20epep\x20config',
        'tr:nth-child(6)\x20td:nth-child(2)',
        'Query\x20is\x20required',
        '\x0a📌\x20Fuente:\x20',
        'MToWV',
        'mfsearch',
        'tr:nth-child(5)\x20td:nth-child(2)',
        '❌\x20No\x20se\x20encontró\x20nada,\x20prueba\x20con\x20otra\x20búsqueda',
        'source_title',
        '428684twqCcn',
        '3858897SjmnkF',
        'mediafiresearch\x20<query>',
        'GfhaQ',
        'UIVjY',
        '🔍\x20Buscando\x20archivos...',
        'href',
        'Bwfoy',
        'filesize',
        'div.info\x20tbody\x20tr:nth-child(4)\x20td:nth-child(2)\x20script',
        'message',
        '\x0a🔗\x20Link:\x20',
        'attr',
        '9zHcoJA',
        'url',
        'https://mediafiretrend.com',
        'UsODb',
        'source_url',
        'FdMQg',
        '1031107bOFgjm',
        '2108412pxYSQu',
        'GdXsK',
        'reply',
        'get',
        'No\x20se\x20pudo\x20decodificar\x20el\x20enlace',
        'command',
        '&search=Search',
        'map',
        '19335416bNLwxl',
        'ZVuNp',
        'help',
        'tbody\x20tr\x20a[href*=\x22/f/\x22]',
        'join',
        'search',
        'LNwSE',
        'floor',
        'https://mediafiretrend.com/?q='
    ];
    _0x2adc = function () {
        return _0x206b88;
    };
    return _0x2adc();
}
(function (_0x3fb0ce, _0x1d23f7) {
    const _0xa60489 = _0x4e9f, _0x3f6c82 = _0x3fb0ce();
    while (!![]) {
        try {
            const _0x1fabb8 = -parseInt(_0xa60489(0x1d6)) / (-0x1a * -0xb2 + -0x11a5 + -0x6e) + -parseInt(_0xa60489(0x1d7)) / (-0x650 + 0x229 + 0x429) + parseInt(_0xa60489(0x1f2)) / (0x2b9 * -0xd + -0xa1d + -0x10f * -0x2b) + -parseInt(_0xa60489(0x1ff)) / (-0x2 * -0x6df + 0x110a + -0x1ec4) * (parseInt(_0xa60489(0x1ee)) / (-0xae2 + 0xa1b + 0xcc)) + parseInt(_0xa60489(0x1e9)) / (-0x1 * 0xa76 + -0x2064 + 0xab8 * 0x4) + -parseInt(_0xa60489(0x200)) / (-0x7 * 0x3a1 + -0x1 * -0x1a5a + -0x76 * 0x2) + -parseInt(_0xa60489(0x1df)) / (0x2 * -0xbff + 0x16db * -0x1 + 0x2ee1) * (-parseInt(_0xa60489(0x1d0)) / (0x1 * 0x1a4e + -0x8 * -0x1dd + -0x292d));
            if (_0x1fabb8 === _0x1d23f7)
                break;
            else
                _0x3f6c82['push'](_0x3f6c82['shift']());
        } catch (_0x46c475) {
            _0x3f6c82['push'](_0x3f6c82['shift']());
        }
    }
}(_0x2adc, -0x2b06 * -0xa + -0x57c7 * -0xa + 0x461b2));
import _0x468dcc from 'axios';
import * as _0x3a710a from 'cheerio';
function shuffle(_0x46066d) {
    const _0x105e28 = _0x4e9f, _0x25e1dc = {
            'IDLXo': function (_0x5a2c38, _0x2f2d18) {
                return _0x5a2c38 - _0x2f2d18;
            },
            'LNwSE': function (_0x5ac91f, _0x5ae21f) {
                return _0x5ac91f > _0x5ae21f;
            },
            'AzISJ': function (_0x52ab0a, _0x872c4) {
                return _0x52ab0a * _0x872c4;
            },
            'UsODb': function (_0x4bd4cc, _0x310d65) {
                return _0x4bd4cc + _0x310d65;
            }
        };
    for (let _0x1abe1a = _0x25e1dc['IDLXo'](_0x46066d[_0x105e28(0x1f1)], 0x129b * -0x1 + 0x229a + 0x2 * -0x7ff); _0x25e1dc[_0x105e28(0x1e5)](_0x1abe1a, 0x5cb * 0x2 + -0xc42 * 0x2 + 0xcee); _0x1abe1a--) {
        const _0x441e32 = Math[_0x105e28(0x1e6)](_0x25e1dc['AzISJ'](Math[_0x105e28(0x1f3)](), _0x25e1dc[_0x105e28(0x1d3)](_0x1abe1a, 0x10 * -0x18d + -0x1134 + 0x2a05)));
        [_0x46066d[_0x1abe1a], _0x46066d[_0x441e32]] = [
            _0x46066d[_0x441e32],
            _0x46066d[_0x1abe1a]
        ];
    }
    return _0x46066d;
}
async function mfsearch(_0x5b3e9e) {
    const _0x59e497 = _0x4e9f, _0x17ff46 = {
            'ZVuNp': function (_0x86897c, _0x540c05) {
                return _0x86897c(_0x540c05);
            },
            'CGpYN': _0x59e497(0x1db),
            'bWgwU': function (_0x12ae97, _0x35993c) {
                return _0x12ae97(_0x35993c);
            },
            'UIVjY': function (_0x3e41d5, _0x5adbb9) {
                return _0x3e41d5(_0x5adbb9);
            },
            'GbLmn': _0x59e497(0x1ea),
            'MToWV': 'tr:nth-child(3)\x20td:nth-child(2)',
            'GdXsK': function (_0x10039c, _0x4fbec1) {
                return _0x10039c(_0x4fbec1);
            },
            'YCzmE': _0x59e497(0x1c9),
            'LAutg': _0x59e497(0x1fc),
            'GfhaQ': _0x59e497(0x1f7),
            'FdMQg': _0x59e497(0x1f8)
        };
    if (!_0x5b3e9e)
        throw new Error(_0x17ff46[_0x59e497(0x1d5)]);
    const {data: _0x266356} = await _0x468dcc[_0x59e497(0x1da)](_0x59e497(0x1e7) + encodeURIComponent(_0x5b3e9e) + _0x59e497(0x1dd)), _0x2c9390 = _0x3a710a[_0x59e497(0x1e8)](_0x266356), _0x14e11b = _0x17ff46[_0x59e497(0x1d8)](shuffle, _0x17ff46[_0x59e497(0x1e0)](_0x2c9390, _0x59e497(0x1e2))['map']((_0x50b394, _0x3d7d31) => _0x2c9390(_0x3d7d31)[_0x59e497(0x1cf)](_0x59e497(0x1c9)))['get']())['slice'](-0x1f24 + -0xb21 * 0x1 + 0x2a45, -0x570 + 0xaf0 + -0x57b), _0x318b6b = await Promise[_0x59e497(0x1f4)](_0x14e11b[_0x59e497(0x1de)](async _0x376ce6 => {
            const _0x350912 = _0x59e497, {data: _0x19ff0a} = await _0x468dcc[_0x350912(0x1da)](_0x350912(0x1d2) + _0x376ce6), _0x24d775 = _0x3a710a[_0x350912(0x1e8)](_0x19ff0a), _0x17bb4d = _0x17ff46[_0x350912(0x1e0)](_0x24d775, _0x350912(0x1cc))[_0x350912(0x1f5)](), _0x543281 = _0x17bb4d[_0x350912(0x1ef)](/unescape\(['"`]([^'"`]+)['"`]\)/);
            if (!_0x543281)
                throw new Error(_0x17ff46['CGpYN']);
            const _0x8d58c4 = _0x3a710a[_0x350912(0x1e8)](_0x17ff46['bWgwU'](decodeURIComponent, _0x543281[-0x25af + 0x589 * 0x3 + 0x1515]));
            return {
                'filename': _0x17ff46[_0x350912(0x1c7)](_0x24d775, _0x17ff46['GbLmn'])[_0x350912(0x1f5)]()[_0x350912(0x1f0)](),
                'filesize': _0x17ff46[_0x350912(0x1c7)](_0x24d775, _0x17ff46[_0x350912(0x1fa)])['text']()[_0x350912(0x1f0)](),
                'url': _0x17ff46[_0x350912(0x1d8)](_0x8d58c4, 'a')[_0x350912(0x1cf)](_0x17ff46['YCzmE']),
                'source_url': _0x17ff46['bWgwU'](_0x24d775, _0x17ff46[_0x350912(0x1ec)])['text']()[_0x350912(0x1f0)](),
                'source_title': _0x17ff46[_0x350912(0x1d8)](_0x24d775, _0x17ff46[_0x350912(0x1c6)])[_0x350912(0x1f5)]()[_0x350912(0x1f0)]()
            };
        }));
    return _0x318b6b;
}
let handler = async (_0x4bcb7a, {text: _0x579ec0}) => {
    const _0x2ddfb1 = _0x4e9f, _0x5f0d3d = {
            'uCOQp': function (_0x1da2de, _0x355878) {
                return _0x1da2de(_0x355878);
            },
            'Bwfoy': _0x2ddfb1(0x1fd)
        };
    if (!_0x579ec0)
        return _0x4bcb7a[_0x2ddfb1(0x1d9)](_0x2ddfb1(0x1f6));
    _0x4bcb7a[_0x2ddfb1(0x1d9)](_0x2ddfb1(0x1c8));
    try {
        let _0x5d70e8 = await _0x5f0d3d['uCOQp'](mfsearch, _0x579ec0);
        if (!_0x5d70e8[_0x2ddfb1(0x1f1)])
            return _0x4bcb7a[_0x2ddfb1(0x1d9)](_0x5f0d3d[_0x2ddfb1(0x1ca)]);
        let _0x16db56 = _0x5d70e8['map']((_0x86622b, _0x4ae037) => _0x4ae037 + (0x5 * 0x656 + -0x1d15 + 0x53 * -0x8) + '.\x20' + _0x86622b['filename'] + '\x0a📦\x20Tamaño:\x20' + _0x86622b[_0x2ddfb1(0x1cb)] + _0x2ddfb1(0x1ce) + _0x86622b[_0x2ddfb1(0x1d1)] + _0x2ddfb1(0x1f9) + _0x86622b[_0x2ddfb1(0x1fe)] + '\x20(' + _0x86622b[_0x2ddfb1(0x1d4)] + ')')[_0x2ddfb1(0x1e3)]('\x0a\x0a');
        await _0x4bcb7a[_0x2ddfb1(0x1d9)](_0x16db56);
    } catch (_0x7f2f3a) {
        _0x4bcb7a[_0x2ddfb1(0x1d9)](_0x2ddfb1(0x1eb) + _0x7f2f3a[_0x2ddfb1(0x1cd)]);
    }
};
function _0x4e9f(_0xf6e21d, _0xb2d4de) {
    const _0x24ef66 = _0x2adc();
    return _0x4e9f = function (_0x802a50, _0x48bfc7) {
        _0x802a50 = _0x802a50 - (0x10ce + 0x25db * 0x1 + 0x14 * -0x2a5);
        let _0x3a54e0 = _0x24ef66[_0x802a50];
        return _0x3a54e0;
    }, _0x4e9f(_0xf6e21d, _0xb2d4de);
}
handler[_0xb2dfd(0x1e1)] = [_0xb2dfd(0x1c5)], handler['tags'] = [_0xb2dfd(0x1e4)], handler[_0xb2dfd(0x1dc)] = [
    _0xb2dfd(0x1fb),
    _0xb2dfd(0x1ed)
];
export default handler;
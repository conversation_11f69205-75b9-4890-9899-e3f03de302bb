
let handler = async (m, { bot, text, usedPrefix, command, args, participants, isOwner }) => {

  let time = global.db.data.users[m.sender].lastjoin + 86400000
  let linkRegex = /chat.whatsapp.com\/([0-9A-Za-z]{20,24})/i
  let delay = time => new Promise(res => setTimeout(res, time))

  let name = m.sender 
  let [_, code] = text.match(linkRegex) || []
  if (!args[0]) throw `✳️ Envie el link del Grupo\n\n 📌 Ejemplo:\n *${usedPrefix + command}* <linkwa> <dias>\n\n_el número son los días que el bot estará en el grupo_` 
  if (!code) throw `✳️ Link inválido`
  if (!args[1]) throw `📌 Falta el número de días\n\n Ejemplo:\n *${usedPrefix + command}* <linkwa> 2`
  if (isNaN(args[1])) throw `✳️ Solo número, que representa los días que el bot estará en el grupo!`
  let owbot = global.owner[1] 
  m.reply(`😎 Espere 3 segundos, me uniré al grupo`)
  await delay(3000)
  try {
  let res = await bot.groupAcceptInvite(code)
  let b = await bot.getGroupMetadata(res)
  let d = b.(await bot.getGroupMetadata(m.chat)).admins
  let member = d.toString()
  let e = await d.filter(v => v.endsWith(owbot + ''))
  let nDays = 86400000 * args[1]  
  let now = new Date() * 1
  if (now < global.db.data.chats[res].expired) global.db.data.chats[res].expired += nDays
  else global.db.data.chats[res].expired = now + nDays
  if (e.length) await m.reply(`✅ Me uni correctamente al grupo \n\n≡ Info del grupo \n\n *Nombre :* ${await bot.getName(res)}\n\nEl bot saldrá automáticamente después de \n\n${msToDate(global.db.data.chats[res].expired - now)}`)

 if (e.length) await bot.reply(res, `🏮 Hola shavales

@${owbot} es mi creador  si tiene alguna duda
fui invitado por *${m.name}*`, m, {
    mentions: d
     }).then(async () => {
     await delay(7000)
     }).then( async () => {
     await bot.reply(res, `vale todos relajaos 🤭`, 0)
     await bot.reply(global.owner[1]+'', `≡ *INVITACIÓN A GRUPO*\n\n@${m.sender.split('@')[0]} ha invitado a *${bot.user.name}* al grupo\n\n*${await bot.getName(res)}*\n\n*ID* : ${res}\n\n📌 Enlace : ${args[0]}\n\nEl bot saldrá automáticamente después de \n\n${msToDate(global.db.data.chats[res].expired - now)}`, null, {mentions: [m.sender]})
     })
     if (!e.length) await bot.reply(global.owner[1]+'', `≡ *INVITACIÓN A GRUPO*\n\n@${m.sender.split('@')[0]} ha invitado a *${bot.user.name}* al grupo\n\n*${await bot.getName(res)}*\n\n*ID* : ${res}\n\n📌 Enlace : ${args[0]}\n\nEl bot saldrá automáticamente después de\n\n ${msToDate(global.db.data.chats[res].expired - now)}`, null, {mentions: [m.sender]})
     if (!e.length) await m.reply(`✅ Se invito al bot al grupo\n\n${await bot.getName(res)}\n\nEl bot saldrá automáticamente después de \n${msToDate(global.db.data.chats[res].expired - now)}`).then(async () => {
     let mes = `Hola a todos 👋🏻
     
*${bot.user.name}* es uno de los bots multidispositivo de WhatsApp construido con Node.js, *${bot.user.name}* Recién invitado por *${m.name}*

para ver el Menu del bot escribe

*${usedPrefix}. menu*

@${bot.user.jid.split('@')[0]} saldrá automáticamente después de \n\n${msToDate(global.db.data.chats[res].expired - now)}`
  await bot.reply(res, mes, m, {
        mentions: d
         })
     })
    } catch (e) {
      bot.reply(global.owner[1]+'', e)
      throw `✳️ Lo siento, el bot no puede unirse a grupos`
      }
}
handler.help = ['join <chat.whatsapp.com> <dias>']
handler.tags = ['owner']
handler.command = ['join', 'invite'] 

handler.owner = true

export default handler

function msToDate(ms) {
  let d = isNaN(ms) ? '--' : Math.floor(ms / 86400000)
  let h = isNaN(ms) ? '--' : Math.floor(ms / 3600000) % 24
  let m = isNaN(ms) ? '--' : Math.floor(ms / 60000) % 60
  let s = isNaN(ms) ? '--' : Math.floor(ms / 1000) % 60
  return [d, 'd ', h, 'h ', m, 'm ', s, 's '].map(v => v.toString().padStart(2, 0)).join('')
}